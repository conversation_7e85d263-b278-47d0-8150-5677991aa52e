part of 'app_pages.dart';

abstract class Routes {
  static const LOGIN = _Paths.LOGIN;
  static const FORGOT_PASSWORD = _Paths.FORGOT_PASSWORD;
  static const DASHBOARD = _Paths.DASHBOARD;
  static const USER_MANAGEMENT = _Paths.USER_MANAGEMENT;
  static const CONTENT_MODERATION = _Paths.CONTENT_MODERATION;
  static const REPORTS = _Paths.REPORTS;
  static const USER_PROFILE = _Paths.USER_PROFILE;
  static const ORDER_DETAILS = _Paths.ORDER_DETAILS;
  static const ACTIVE_ORDERS = _Paths.ACTIVE_ORDERS;
  Routes._();
}

abstract class _Paths {
  static const LOGIN = '/login';
  static const FORGOT_PASSWORD = '/forgot-password';
  static const DASHBOARD = '/dashboard';
  static const USER_MANAGEMENT = '/user-management';
  static const CONTENT_MODERATION = '/content-moderation';
  static const REPORTS = '/reports';
  static const USER_PROFILE = '/user-profile';
  static const ORDER_DETAILS = '/order-details';
  static const ACTIVE_ORDERS = '/active-orders';
  _Paths._();
}
