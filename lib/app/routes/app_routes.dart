part of 'app_pages.dart';

abstract class Routes {
  Routes._();
  static const LOGIN = _Paths.LOGIN;
  static const DASHBOARD = _Paths.DASHBOARD;
  static const USER_MANAGEMENT = _Paths.USER_MANAGEMENT;
  static const CONTENT_MODERATION = _Paths.CONTENT_MODERATION;
  static const REPORTS = _Paths.REPORTS;
}

abstract class _Paths {
  _Paths._();
  static const LOGIN = '/login';
  static const DASHBOARD = '/dashboard';
  static const USER_MANAGEMENT = '/user-management';
  static const CONTENT_MODERATION = '/content-moderation';
  static const REPORTS = '/reports';
}
