import 'package:flutter/material.dart';

/// App color constants based on the new design system
class AppColors {
  // Primary colors
  static const Color appColor = Color(0xffE31C5D);
  static const Color appColorLight = Color(0xffad6c81);
  static const Color clrGrey = Color(0xff2D2B2E);
  
  // App constants
  static const double minWithdrawalAmount = 10.0;
  static const int oneUnitPoints = 100;
  static const String currencyUnit = "NOK";
  
  // Additional theme colors
  static const Color scaffoldBackground = Color(0xFFFAFBFF);
  static const Color cardBackground = Colors.white;
  static const Color floatingActionButtonBackground = Color(0xFFF1F1F1);
  
  // Status colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);
  
  // Text colors
  static const Color primaryTextColor = Colors.black;
  static const Color secondaryTextColor = Color(0xff585858);
  
  // Border colors
  static const Color borderColor = Colors.black;
  static const Color checkboxBorderColor = Color(0xff585858);
}
