import 'package:flutter/material.dart';

// Enhanced User Model
class UserModel {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String status;
  final DateTime joinDate;
  final String profileImage;
  final bool isVerified;
  final String bio;
  final String location;
  final double rating;
  final int totalOrders;
  final int completedOrders;
  final List<String> skills;
  final DateTime lastActive;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.status,
    required this.joinDate,
    required this.profileImage,
    required this.isVerified,
    required this.bio,
    required this.location,
    required this.rating,
    required this.totalOrders,
    required this.completedOrders,
    required this.skills,
    required this.lastActive,
  });
}

// Service Model
class ServiceModel {
  final int id;
  final String title;
  final String description;
  final String category;
  final double price;
  final String currency;
  final int deliveryTime;
  final List<String> images;
  final int sellerId;
  final String sellerName;
  final String sellerImage;
  final double rating;
  final int reviewCount;
  final String status;
  final DateTime createdAt;
  final List<String> tags;

  ServiceModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    required this.currency,
    required this.deliveryTime,
    required this.images,
    required this.sellerId,
    required this.sellerName,
    required this.sellerImage,
    required this.rating,
    required this.reviewCount,
    required this.status,
    required this.createdAt,
    required this.tags,
  });
}

// Order Model
class OrderModel {
  final int id;
  final int serviceId;
  final String serviceTitle;
  final int buyerId;
  final String buyerName;
  final String buyerImage;
  final int sellerId;
  final String sellerName;
  final String sellerImage;
  final double amount;
  final String currency;
  final String status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final DateTime? dueDate;
  final String requirements;
  final List<String> deliverables;
  final int? buyerRating;
  final int? sellerRating;
  final String? buyerReview;
  final String? sellerReview;

  OrderModel({
    required this.id,
    required this.serviceId,
    required this.serviceTitle,
    required this.buyerId,
    required this.buyerName,
    required this.buyerImage,
    required this.sellerId,
    required this.sellerName,
    required this.sellerImage,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.dueDate,
    required this.requirements,
    required this.deliverables,
    this.buyerRating,
    this.sellerRating,
    this.buyerReview,
    this.sellerReview,
  });
}

// Chat Message Model
class ChatMessageModel {
  final int id;
  final int orderId;
  final int senderId;
  final String senderName;
  final String message;
  final DateTime timestamp;
  final String type; // text, image, file
  final String? attachmentUrl;
  final bool isRead;

  ChatMessageModel({
    required this.id,
    required this.orderId,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    required this.type,
    this.attachmentUrl,
    required this.isRead,
  });
}

// Review Model
class ReviewModel {
  final int id;
  final int orderId;
  final int reviewerId;
  final String reviewerName;
  final String reviewerImage;
  final int revieweeId;
  final String revieweeName;
  final int rating;
  final String comment;
  final DateTime createdAt;

  ReviewModel({
    required this.id,
    required this.orderId,
    required this.reviewerId,
    required this.reviewerName,
    required this.reviewerImage,
    required this.revieweeId,
    required this.revieweeName,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });
}

// Admin Model
class AdminModel {
  final int id;
  final String name;
  final String email;
  final String role; // super_admin, admin, moderator
  final List<String> permissions;
  final DateTime createdAt;
  final DateTime lastLogin;
  final bool isActive;
  final String profileImage;

  AdminModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.permissions,
    required this.createdAt,
    required this.lastLogin,
    required this.isActive,
    required this.profileImage,
  });
}

// Activity Model
class ActivityModel {
  final int id;
  final String action;
  final String description;
  final int adminId;
  final String adminName;
  final DateTime timestamp;
  final String targetType; // user, order, service, report
  final int? targetId;
  final Map<String, dynamic>? metadata;

  ActivityModel({
    required this.id,
    required this.action,
    required this.description,
    required this.adminId,
    required this.adminName,
    required this.timestamp,
    required this.targetType,
    this.targetId,
    this.metadata,
  });
}

// Report Model (Enhanced)
class ReportModel {
  final int id;
  final String type;
  final int reportedUserId;
  final String reportedUserName;
  final String reportedUserImage;
  final int reportingUserId;
  final String reportingUserName;
  final String reportingUserImage;
  final int? serviceId;
  final String? serviceTitle;
  final int? orderId;
  final String reason;
  final DateTime date;
  final String status;
  final String details;
  final int? handledByAdminId;
  final String? handledByAdminName;
  final DateTime? handledAt;

  ReportModel({
    required this.id,
    required this.type,
    required this.reportedUserId,
    required this.reportedUserName,
    required this.reportedUserImage,
    required this.reportingUserId,
    required this.reportingUserName,
    required this.reportingUserImage,
    this.serviceId,
    this.serviceTitle,
    this.orderId,
    required this.reason,
    required this.date,
    required this.status,
    required this.details,
    this.handledByAdminId,
    this.handledByAdminName,
    this.handledAt,
  });
}

// Order Status Enum
enum OrderStatus {
  pending,
  active,
  inProgress,
  delivered,
  completed,
  cancelled,
  disputed,
  refunded,
}

// Service Status Enum
enum ServiceStatus {
  active,
  paused,
  draft,
  rejected,
  suspended,
}

// Admin Permissions
class AdminPermissions {
  static const String userManagement = 'user_management';
  static const String orderManagement = 'order_management';
  static const String serviceManagement = 'service_management';
  static const String reportManagement = 'report_management';
  static const String adminManagement = 'admin_management';
  static const String contentModeration = 'content_moderation';
  static const String analytics = 'analytics';
  static const String systemSettings = 'system_settings';

  static const List<String> allPermissions = [
    userManagement,
    orderManagement,
    serviceManagement,
    reportManagement,
    adminManagement,
    contentModeration,
    analytics,
    systemSettings,
  ];

  static const Map<String, List<String>> rolePermissions = {
    'super_admin': allPermissions,
    'admin': [
      userManagement,
      orderManagement,
      serviceManagement,
      reportManagement,
      contentModeration,
      analytics,
    ],
    'moderator': [
      contentModeration,
      reportManagement,
    ],
  };
}
