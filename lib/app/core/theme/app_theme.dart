import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';

class AppTheme {
  static final ThemeData lightTheme = ThemeData(
    primaryColor: AppColors.appColor,
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(seedColor: AppColors.appColor),
    dialogTheme: const DialogTheme(
      iconColor: Colors.white,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: Colors.white,
      filled: true,
      labelStyle: const TextStyle(color: Colors.black),
      border: DecoratedInputBorder(
        shadow: BoxShadow(
          color: Colors.grey.withValues(alpha: 0.3),
          blurRadius: 8,
          spreadRadius: .05,
        ),
        child: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black, width: .8),
        ),
      ),
    ),
    fontFamily: 'Plus',
    checkboxTheme: CheckboxThemeData(
      checkColor: WidgetStateProperty.all(Colors.white),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      side: const BorderSide(color: AppColors.checkboxBorderColor, width: 1),
    ),
    radioTheme: const RadioThemeData(
      fillColor: WidgetStatePropertyAll(AppColors.appColor),
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      elevation: 10,
      shadowColor: Colors.black.withValues(alpha: .2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
    ),
    switchTheme: const SwitchThemeData(
      trackColor: WidgetStatePropertyAll(AppColors.appColor),
      thumbColor: WidgetStatePropertyAll(Colors.white),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      splashColor: Colors.transparent,
      backgroundColor: AppColors.floatingActionButtonBackground,
      elevation: 0,
    ),
    appBarTheme: const AppBarTheme(
      surfaceTintColor: Colors.white,
      backgroundColor: Colors.white,
      titleTextStyle: TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 20,
        fontFamily: 'Plus',
        decoration: TextDecoration.none,
        color: Colors.black,
      ),
      shadowColor: Colors.black,
      centerTitle: true,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: AppColors.appColor,
        statusBarIconBrightness: Brightness.light,
      ),
      iconTheme: IconThemeData(color: Colors.black),
    ),
    dividerColor: Colors.transparent,
    scaffoldBackgroundColor: AppColors.scaffoldBackground,
  );

  static final ThemeData darkTheme = ThemeData(
    primaryColor: AppColors.appColor,
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.appColor,
      brightness: Brightness.dark,
    ),
    dialogTheme: DialogTheme(
      iconColor: Colors.white,
      backgroundColor: AppColors.clrGrey,
      surfaceTintColor: AppColors.clrGrey,
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: AppColors.clrGrey,
      filled: true,
      labelStyle: const TextStyle(color: Colors.white),
      border: DecoratedInputBorder(
        shadow: BoxShadow(
          color: Colors.black.withValues(alpha: 0.5),
          blurRadius: 8,
          spreadRadius: .05,
        ),
        child: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.white, width: .8),
        ),
      ),
    ),
    fontFamily: 'Plus',
    checkboxTheme: CheckboxThemeData(
      checkColor: WidgetStateProperty.all(Colors.white),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      side: const BorderSide(color: Colors.white, width: 1),
    ),
    radioTheme: const RadioThemeData(
      fillColor: WidgetStatePropertyAll(AppColors.appColor),
    ),
    cardTheme: CardTheme(
      color: AppColors.clrGrey,
      surfaceTintColor: AppColors.clrGrey,
      elevation: 10,
      shadowColor: Colors.black.withValues(alpha: .4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
    ),
    switchTheme: const SwitchThemeData(
      trackColor: WidgetStatePropertyAll(AppColors.appColor),
      thumbColor: WidgetStatePropertyAll(Colors.white),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      splashColor: Colors.transparent,
      backgroundColor: AppColors.clrGrey,
      elevation: 0,
    ),
    appBarTheme: AppBarTheme(
      surfaceTintColor: AppColors.clrGrey,
      backgroundColor: AppColors.clrGrey,
      titleTextStyle: const TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 20,
        fontFamily: 'Plus',
        decoration: TextDecoration.none,
        color: Colors.white,
      ),
      shadowColor: Colors.black,
      centerTitle: true,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: AppColors.appColor,
        statusBarIconBrightness: Brightness.light,
      ),
      iconTheme: const IconThemeData(color: Colors.white),
    ),
    dividerColor: Colors.transparent,
    scaffoldBackgroundColor: const Color(0xFF121212),
  );
}

class DecoratedInputBorder extends InputBorder {
  final BoxShadow shadow;
  final InputBorder child;

  const DecoratedInputBorder({required this.shadow, required this.child});

  @override
  EdgeInsetsGeometry get dimensions => child.dimensions;

  @override
  bool get isOutline => child.isOutline;

  @override
  InputBorder copyWith({BorderSide? borderSide}) {
    return DecoratedInputBorder(
      shadow: shadow,
      child: child.copyWith(borderSide: borderSide),
    );
  }

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return child.getInnerPath(rect, textDirection: textDirection);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return child.getOuterPath(rect, textDirection: textDirection);
  }

  @override
  void paint(
    Canvas canvas,
    Rect rect, {
    double? gapStart,
    double gapExtent = 0.0,
    double gapPercentage = 0.0,
    TextDirection? textDirection,
  }) {
    final Paint paint =
        Paint()
          ..color = shadow.color
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadow.blurRadius);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        rect.shift(shadow.offset),
        const Radius.circular(4),
      ),
      paint,
    );

    child.paint(
      canvas,
      rect,
      gapStart: gapStart,
      gapExtent: gapExtent,
      gapPercentage: gapPercentage,
      textDirection: textDirection,
    );
  }

  @override
  ShapeBorder scale(double t) {
    return DecoratedInputBorder(
      shadow: shadow,
      child: child.scale(t) as InputBorder,
    );
  }
}
