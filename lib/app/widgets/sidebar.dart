import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/modules/dashboard/controllers/dashboard_controller.dart';

class Sidebar extends GetView<DashboardController> {
  const Sidebar({super.key});

  @override
  Widget build(BuildContext context) {

    return Obx(() => AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: controller.isExpanded.value ? 250 : 70,
      color: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF1E1E1E)
          : Colors.white,
      child: Column(
        children: [
          // Logo and toggle button
          Container(
            height: 64,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                if (controller.isExpanded.value) ...[
                  const Text(
                    'Greet Admin',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                ],
                IconButton(
                  icon: Icon(
                    controller.isExpanded.value
                        ? Icons.menu_open
                        : Icons.menu,
                  ),
                  onPressed: controller.toggleSidebar,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          
          // Menu items
          Expanded(
            child: ListView.builder(
              itemCount: controller.menuItems.length,
              itemBuilder: (context, index) {
                final item = controller.menuItems[index];
                return Obx(() => ListTile(
                  selected: controller.selectedIndex.value == index,
                  selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  leading: Icon(
                    item['icon'],
                    color: controller.selectedIndex.value == index
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  title: controller.isExpanded.value
                      ? Text(
                          item['title'],
                          style: TextStyle(
                            color: controller.selectedIndex.value == index
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        )
                      : null,
                  onTap: () => controller.navigateTo(index),
                ));
              },
            ),
          ),
          
          // User profile and logout
          const Divider(height: 1),
          ListTile(
            leading: const CircleAvatar(
              radius: 16,
              child: Icon(Icons.person, size: 20),
            ),
            title: controller.isExpanded.value
                ? const Text('Admin User')
                : null,
            subtitle: controller.isExpanded.value
                ? const Text('<EMAIL>')
                : null,
            onTap: () {},
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: controller.isExpanded.value
                ? const Text('Logout')
                : null,
            onTap: controller.logout,
          ),
          const SizedBox(height: 16),
        ],
      ),
    ));
  }
}