import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';

class DashboardStats extends StatelessWidget {
  const DashboardStats({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount:
          Get.width < 600
              ? 1
              : Get.width < 900
              ? 2
              : 4,
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: const [
        StatCard(
          title: 'Total Users',
          value: '24,532',
          icon: Icons.people,
          color: AppColors.appColor,
          change: '+12%',
        ),
        StatCard(
          title: 'Active Today',
          value: '8,942',
          icon: Icons.person_outline,
          color: AppColors.successColor,
          change: '+5%',
        ),
        StatCard(
          title: 'Pending Reports',
          value: '142',
          icon: Icons.flag_outlined,
          color: AppColors.warningColor,
          change: '-3%',
        ),
        StatCard(
          title: 'Revenue (MTD)',
          value: '\$45,289',
          icon: Icons.attach_money,
          color: AppColors.appColorLight,
          change: '+8%',
        ),
      ],
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String change;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.change,
  });

  @override
  Widget build(BuildContext context) {
    final isPositive = change.startsWith('+');

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                  color:
                      isPositive
                          ? AppColors.successColor
                          : AppColors.errorColor,
                ),
                const SizedBox(width: 4),
                Text(
                  change,
                  style: TextStyle(
                    color:
                        isPositive
                            ? AppColors.successColor
                            : AppColors.errorColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(' from last month'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
