import 'package:flutter/material.dart';

class RecentActivities extends StatelessWidget {
  RecentActivities({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activities',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _activities.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final activity = _activities[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: activity['color'],
                  child: Icon(activity['icon'], color: Colors.white, size: 20),
                ),
                title: Text(activity['title']),
                subtitle: Text(activity['description']),
                trailing: Text(
                  activity['time'],
                  style: const TextStyle(color: Colors.grey),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  final List<Map<String, dynamic>> _activities = [
    {
      'icon': Icons.person_add,
      'color': Colors.green,
      'title': 'New User Registration',
      'description': 'John Doe created a new account',
      'time': '2 min ago',
    },
    {
      'icon': Icons.flag,
      'color': Colors.red,
      'title': 'Content Reported',
      'description': 'Profile photo reported for inappropriate content',
      'time': '15 min ago',
    },
    {
      'icon': Icons.support_agent,
      'color': Colors.blue,
      'title': 'Support Ticket Created',
      'description': 'User requested help with payment issue',
      'time': '1 hour ago',
    },
    {
      'icon': Icons.payment,
      'color': Colors.purple,
      'title': 'Subscription Purchased',
      'description': 'Sarah Johnson upgraded to Premium plan',
      'time': '3 hours ago',
    },
    {
      'icon': Icons.admin_panel_settings,
      'color': Colors.orange,
      'title': 'Admin Action',
      'description': 'Admin approved 15 profile photos',
      'time': '5 hours ago',
    },
  ];
}