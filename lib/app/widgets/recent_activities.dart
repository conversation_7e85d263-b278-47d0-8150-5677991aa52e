import 'package:flutter/material.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';

class RecentActivities extends StatelessWidget {
  final List<Map<String, dynamic>> _activities = [
    {
      'icon': Icons.person_add,
      'color': AppColors.successColor,
      'title': 'New User Registration',
      'description': '<PERSON> created a new account',
      'time': '2 min ago',
    },
    {
      'icon': Icons.flag,
      'color': AppColors.errorColor,
      'title': 'Content Reported',
      'description': 'Profile photo reported for inappropriate content',
      'time': '15 min ago',
    },
    {
      'icon': Icons.support_agent,
      'color': AppColors.infoColor,
      'title': 'Support Ticket Created',
      'description': 'User requested help with payment issue',
      'time': '1 hour ago',
    },
    {
      'icon': Icons.payment,
      'color': AppColors.appColor,
      'title': 'Subscription Purchased',
      'description': '<PERSON> upgraded to Premium plan',
      'time': '3 hours ago',
    },
    {
      'icon': Icons.admin_panel_settings,
      'color': AppColors.warningColor,
      'title': 'Admin Action',
      'description': 'Admin approved 15 profile photos',
      'time': '5 hours ago',
    },
  ];

  RecentActivities({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activities',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _activities.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final activity = _activities[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: activity['color'],
                  child: Icon(activity['icon'], color: Colors.white, size: 20),
                ),
                title: Text(activity['title']),
                subtitle: Text(activity['description']),
                trailing: Text(
                  activity['time'],
                  style: const TextStyle(color: Colors.grey),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
