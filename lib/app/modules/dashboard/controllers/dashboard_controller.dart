import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/routes/app_pages.dart';

class DashboardController extends GetxController {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final RxInt selectedIndex = 0.obs;
  final RxBool isLoading = true.obs;
  final RxBool isExpanded = true.obs;
  
  // Analytics data
  final RxInt totalUsers = 0.obs;
  final RxInt activeUsers = 0.obs;
  final RxInt newUsersToday = 0.obs;
  final RxInt pendingReports = 0.obs;
  final RxInt totalMessages = 0.obs;
  final RxInt totalMatches = 0.obs;
  
  // Chart data
  final RxList<Map<String, dynamic>> userGrowthData = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> activityData = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> genderDistribution = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> ageDistribution = <Map<String, dynamic>>[].obs;

  // Menu items for sidebar
  final List<Map<String, dynamic>> menuItems = [
    {
      'title': 'Dashboard',
      'icon': Icons.dashboard,
      'route': Routes.DASHBOARD,
    },
    {
      'title': 'User Management',
      'icon': Icons.people,
      'route': Routes.USER_MANAGEMENT,
    },
    {
      'title': 'Content Moderation',
      'icon': Icons.content_paste,
      'route': Routes.CONTENT_MODERATION,
    },
    {
      'title': 'Reports',
      'icon': Icons.report_problem,
      'route': Routes.REPORTS,
    },
  ];

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
    
    // Set the initial selected index based on current route
    final currentRoute = Get.currentRoute;
    for (int i = 0; i < menuItems.length; i++) {
      if (menuItems[i]['route'] == currentRoute) {
        selectedIndex.value = i;
        break;
      }
    }
  }

  void loadDashboardData() {
    isLoading.value = true;
    
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      // Set mock data
      totalUsers.value = 15482;
      activeUsers.value = 8743;
      newUsersToday.value = 127;
      pendingReports.value = 42;
      totalMessages.value = 287954;
      totalMatches.value = 9876;
      
      // User growth data (last 7 days)
      userGrowthData.value = [
        {'day': 'Mon', 'users': 95},
        {'day': 'Tue', 'users': 110},
        {'day': 'Wed', 'users': 135},
        {'day': 'Thu', 'users': 120},
        {'day': 'Fri', 'users': 150},
        {'day': 'Sat', 'users': 180},
        {'day': 'Sun', 'users': 127},
      ];
      
      // Activity data (messages, matches, logins)
      activityData.value = [
        {'day': 'Mon', 'messages': 1250, 'matches': 85, 'logins': 950},
        {'day': 'Tue', 'messages': 1400, 'matches': 92, 'logins': 1050},
        {'day': 'Wed', 'messages': 1600, 'matches': 105, 'logins': 1200},
        {'day': 'Thu', 'messages': 1500, 'matches': 98, 'logins': 1100},
        {'day': 'Fri', 'messages': 1800, 'matches': 120, 'logins': 1300},
        {'day': 'Sat', 'messages': 2200, 'matches': 145, 'logins': 1500},
        {'day': 'Sun', 'messages': 1900, 'matches': 130, 'logins': 1400},
      ];
      
      // Gender distribution
      genderDistribution.value = [
        {'gender': 'Male', 'count': 8500},
        {'gender': 'Female', 'count': 6800},
        {'gender': 'Non-binary', 'count': 182},
      ];
      
      // Age distribution
      ageDistribution.value = [
        {'age': '18-24', 'count': 5200},
        {'age': '25-34', 'count': 7300},
        {'age': '35-44', 'count': 2100},
        {'age': '45-54', 'count': 650},
        {'age': '55+', 'count': 232},
      ];
      
      isLoading.value = false;
    });
  }

  void toggleDrawer() {
    if (scaffoldKey.currentState!.isDrawerOpen) {
      scaffoldKey.currentState!.closeDrawer();
    } else {
      scaffoldKey.currentState!.openDrawer();
    }
  }

  void toggleSidebar() {
    isExpanded.value = !isExpanded.value;
  }

  void navigateTo(int index) {
    if (selectedIndex.value != index) {
      selectedIndex.value = index;
      Get.toNamed(menuItems[index]['route']);
    }
  }

  void logout() {
    // Show confirmation dialog
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // Navigate to login page
              Get.offAllNamed('/login');
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
