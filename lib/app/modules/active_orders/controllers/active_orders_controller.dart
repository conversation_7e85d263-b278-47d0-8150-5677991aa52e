import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/models/app_models.dart';

class ActiveOrdersController extends GetxController {
  final RxBool isLoading = true.obs;
  final RxList<OrderModel> activeOrders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;
  final RxString selectedFilter = 'All'.obs;
  final searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadActiveOrders();
  }

  void loadActiveOrders() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      activeOrders.value = _generateMockActiveOrders();
      filteredOrders.value = List.from(activeOrders);
      isLoading.value = false;
    });
  }

  void filterOrders(String filter) {
    selectedFilter.value = filter;
    if (filter == 'All') {
      filteredOrders.value = List.from(activeOrders);
    } else {
      filteredOrders.value = activeOrders.where((order) => order.status == filter.toLowerCase()).toList();
    }
  }

  void search(String query) {
    if (query.isEmpty) {
      filterOrders(selectedFilter.value);
    } else {
      filteredOrders.value = activeOrders.where((order) {
        return order.serviceTitle.toLowerCase().contains(query.toLowerCase()) ||
            order.buyerName.toLowerCase().contains(query.toLowerCase()) ||
            order.sellerName.toLowerCase().contains(query.toLowerCase()) ||
            order.id.toString().contains(query);
      }).toList();
    }
  }

  void openOrderDetails(int orderId) {
    Get.toNamed('/order-details', arguments: {'orderId': orderId});
  }

  void openUserProfile(int userId) {
    Get.toNamed('/user-profile', arguments: {'userId': userId});
  }

  List<OrderModel> _generateMockActiveOrders() {
    final statuses = ['active', 'in_progress', 'delivered', 'pending'];
    final services = [
      'Logo Design',
      'Website Development',
      'Content Writing',
      'Social Media Management',
      'Video Editing',
      'Graphic Design',
      'SEO Optimization',
      'Mobile App Development',
    ];
    
    return List.generate(25, (index) {
      final status = statuses[index % statuses.length];
      final service = services[index % services.length];
      
      return OrderModel(
        id: 5000 + index,
        serviceId: 100 + index,
        serviceTitle: service,
        buyerId: 2000 + index,
        buyerName: 'Buyer ${index + 1}',
        buyerImage: 'https://i.pravatar.cc/150?img=${index + 10}',
        sellerId: 1000 + index,
        sellerName: 'Seller ${index + 1}',
        sellerImage: 'https://i.pravatar.cc/150?img=${index + 20}',
        amount: 50.0 + (index * 25),
        currency: 'USD',
        status: status,
        createdAt: DateTime.now().subtract(Duration(days: index)),
        completedAt: status == 'completed' ? DateTime.now().subtract(Duration(days: index ~/ 2)) : null,
        dueDate: DateTime.now().add(Duration(days: 7 - (index % 7))),
        requirements: 'Requirements for $service order',
        deliverables: ['Deliverable 1', 'Deliverable 2'],
        buyerRating: status == 'completed' ? 4 + (index % 2) : null,
        sellerRating: status == 'completed' ? 5 : null,
        buyerReview: status == 'completed' ? 'Great work!' : null,
        sellerReview: status == 'completed' ? 'Excellent client!' : null,
      );
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
