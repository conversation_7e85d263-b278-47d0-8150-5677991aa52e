import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';
import 'package:greet_admin/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:greet_admin/app/widgets/app_header.dart';
import 'package:greet_admin/app/widgets/sidebar.dart';
import '../controllers/active_orders_controller.dart';

class ActiveOrdersView extends GetView<ActiveOrdersController> {
  const ActiveOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    final dashboardController = Get.find<DashboardController>();

    return Scaffold(
      key: dashboardController.scaffoldKey,
      drawer: Get.width < 1200 ? const Sidebar() : null,
      body: Row(
        children: [
          if (Get.width >= 1200) const Sidebar(),
          Expanded(
            child: Column(
              children: [
                const AppHeader(title: 'Active Orders'),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Search and filter bar
                        Get.width < 600 
                          ? Column(
                              children: [
                                TextField(
                                  controller: controller.searchController,
                                  decoration: InputDecoration(
                                    hintText: 'Search orders...',
                                    prefixIcon: const Icon(Icons.search),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  onChanged: controller.search,
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: Obx(
                                    () => DropdownButton<String>(
                                      value: controller.selectedFilter.value,
                                      isExpanded: true,
                                      items: [
                                        'All',
                                        'Active',
                                        'In Progress',
                                        'Delivered',
                                        'Pending',
                                      ].map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.filterOrders(value);
                                        }
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: controller.searchController,
                                    decoration: InputDecoration(
                                      hintText: 'Search orders...',
                                      prefixIcon: const Icon(Icons.search),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    onChanged: controller.search,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Obx(
                                  () => DropdownButton<String>(
                                    value: controller.selectedFilter.value,
                                    items: [
                                      'All',
                                      'Active',
                                      'In Progress',
                                      'Delivered',
                                      'Pending',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        controller.filterOrders(value);
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                        const SizedBox(height: 16),

                        // Orders table
                        Expanded(
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (controller.filteredOrders.isEmpty) {
                              return const Center(
                                child: Text('No orders found'),
                              );
                            }

                            return Card(
                              elevation: 2,
                              child: SizedBox(
                                width: double.infinity,
                                child: SingleChildScrollView(
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(
                                        minWidth: Get.width > 1200 
                                            ? Get.width - 250 - 32 
                                            : Get.width - 32,
                                      ),
                                      child: DataTable(
                                        columnSpacing: 20,
                                        columns: const [
                                          DataColumn(label: Text('Order ID')),
                                          DataColumn(label: Text('Service')),
                                          DataColumn(label: Text('Buyer')),
                                          DataColumn(label: Text('Seller')),
                                          DataColumn(label: Text('Amount')),
                                          DataColumn(label: Text('Status')),
                                          DataColumn(label: Text('Due Date')),
                                          DataColumn(label: Text('Actions')),
                                        ],
                                        rows: controller.filteredOrders.map((order) {
                                          return DataRow(
                                            onSelectChanged: (selected) {
                                              if (selected == true) {
                                                controller.openOrderDetails(order.id);
                                              }
                                            },
                                            cells: [
                                              DataCell(Text('#${order.id}')),
                                              DataCell(
                                                SizedBox(
                                                  width: 150,
                                                  child: Text(
                                                    order.serviceTitle,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ),
                                              DataCell(
                                                GestureDetector(
                                                  onTap: () => controller.openUserProfile(order.buyerId),
                                                  child: Row(
                                                    children: [
                                                      CircleAvatar(
                                                        radius: 16,
                                                        backgroundImage: NetworkImage(order.buyerImage),
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        order.buyerName,
                                                        style: const TextStyle(
                                                          color: AppColors.appColor,
                                                          decoration: TextDecoration.underline,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              DataCell(
                                                GestureDetector(
                                                  onTap: () => controller.openUserProfile(order.sellerId),
                                                  child: Row(
                                                    children: [
                                                      CircleAvatar(
                                                        radius: 16,
                                                        backgroundImage: NetworkImage(order.sellerImage),
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        order.sellerName,
                                                        style: const TextStyle(
                                                          color: AppColors.appColor,
                                                          decoration: TextDecoration.underline,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              DataCell(Text('${order.currency} ${order.amount}')),
                                              DataCell(
                                                Container(
                                                  padding: const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: _getStatusColor(order.status),
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: Text(
                                                    order.status.toUpperCase(),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              DataCell(
                                                Text(
                                                  '${order.dueDate?.day}/${order.dueDate?.month}/${order.dueDate?.year}',
                                                ),
                                              ),
                                              DataCell(
                                                IconButton(
                                                  icon: const Icon(Icons.arrow_forward),
                                                  onPressed: () => controller.openOrderDetails(order.id),
                                                  tooltip: 'View Details',
                                                ),
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.blue;
      case 'in_progress':
        return Colors.orange;
      case 'delivered':
        return Colors.purple;
      case 'pending':
        return Colors.grey;
      case 'completed':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
