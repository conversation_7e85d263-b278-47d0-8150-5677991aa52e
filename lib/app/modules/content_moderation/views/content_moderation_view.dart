import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';
import 'package:greet_admin/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:greet_admin/app/widgets/app_header.dart';
import 'package:greet_admin/app/widgets/sidebar.dart';

import '../controllers/content_moderation_controller.dart';

class ContentModerationView extends GetView<ContentModerationController> {
  const ContentModerationView({super.key});

  @override
  Widget build(BuildContext context) {
    final dashboardController = Get.find<DashboardController>();

    return Scaffold(
      key: dashboardController.scaffoldKey,
      drawer: Get.width < 1200 ? const Sidebar() : null,
      body: Row(
        children: [
          // Sidebar for large screens
          if (Get.width >= 1200) const Sidebar(),

          // Main content
          Expanded(
            child: Column(
              children: [
                // Header
                const AppHeader(title: 'Content Moderation'),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Search and filter bar
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: controller.searchController,
                                decoration: InputDecoration(
                                  hintText: 'Search content...',
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                onChanged: controller.search,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Obx(
                              () => DropdownButton<String>(
                                value: controller.selectedFilter.value,
                                items:
                                    [
                                      'All',
                                      'Pending',
                                      'Approved',
                                      'Rejected',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.filterByStatus(value);
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Obx(
                              () => DropdownButton<String>(
                                value: controller.selectedType.value,
                                items:
                                    [
                                      'All',
                                      'Profile Photo',
                                      'Bio Text',
                                      'Message',
                                      'Comment',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.filterByType(value);
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Content grid
                        Expanded(
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (controller.filteredItems.isEmpty) {
                              return const Center(
                                child: Text('No content found'),
                              );
                            }

                            return GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount:
                                        Get.width < 600
                                            ? 1
                                            : Get.width < 900
                                            ? 2
                                            : Get.width < 1200
                                            ? 3
                                            : 4,
                                    childAspectRatio: 0.8,
                                    crossAxisSpacing: 16,
                                    mainAxisSpacing: 16,
                                  ),
                              itemCount: controller.filteredItems.length,
                              itemBuilder: (context, index) {
                                final item = controller.filteredItems[index];
                                return Card(
                                  elevation: 2,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Content preview
                                      Expanded(
                                        child:
                                            item.type == 'Profile Photo'
                                                ? Image.network(
                                                  item.content,
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                )
                                                : Container(
                                                  padding: const EdgeInsets.all(
                                                    8,
                                                  ),
                                                  width: double.infinity,
                                                  color: Colors.grey[200],
                                                  child: Text(
                                                    item.content,
                                                    style: const TextStyle(
                                                      fontSize: 14,
                                                    ),
                                                    maxLines: 5,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                      ),

                                      // Content info
                                      Padding(
                                        padding: const EdgeInsets.all(8),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  'ID: #${item.id}',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color: _getStatusColor(
                                                      item.status,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    item.status,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Type: ${item.type}',
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                            Text(
                                              'Reason: ${item.reportReason}',
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                            Text(
                                              'Reported by: ${item.reportedBy}',
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                            Text(
                                              'Date: ${item.reportDate.day}/${item.reportDate.month}/${item.reportDate.year}',
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                ElevatedButton.icon(
                                                  icon: const Icon(
                                                    Icons.check,
                                                    size: 16,
                                                  ),
                                                  label: const Text('Approve'),
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        Colors.green,
                                                    foregroundColor:
                                                        Colors.white,
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 8,
                                                        ),
                                                  ),
                                                  onPressed:
                                                      () => controller
                                                          .approveContent(
                                                            item.id,
                                                          ),
                                                ),
                                                ElevatedButton.icon(
                                                  icon: const Icon(
                                                    Icons.close,
                                                    size: 16,
                                                  ),
                                                  label: const Text('Reject'),
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor: Colors.red,
                                                    foregroundColor:
                                                        Colors.white,
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 8,
                                                        ),
                                                  ),
                                                  onPressed:
                                                      () => controller
                                                          .rejectContent(
                                                            item.id,
                                                          ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Approved':
        return AppColors.successColor;
      case 'Rejected':
        return AppColors.errorColor;
      case 'Pending':
        return AppColors.warningColor;
      default:
        return AppColors.clrGrey;
    }
  }
}
