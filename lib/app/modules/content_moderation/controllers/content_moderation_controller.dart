import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContentItem {
  final int id;
  final String type;
  final String content;
  final String reportReason;
  final DateTime reportDate;
  final String reportedBy;
  final String status;
  final double aiConfidence;

  ContentItem({
    required this.id,
    required this.type,
    required this.content,
    required this.reportReason,
    required this.reportDate,
    required this.reportedBy,
    required this.status,
    required this.aiConfidence,
  });
}

class ContentModerationController extends GetxController {
  final RxList<ContentItem> contentItems = <ContentItem>[].obs;
  final RxList<ContentItem> filteredItems = <ContentItem>[].obs;
  final RxBool isLoading = true.obs;
  final RxString selectedFilter = 'All'.obs;
  final RxString selectedType = 'All'.obs;
  final searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadContent();
  }

  void loadContent() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      contentItems.value = _generateMockContent();
      filteredItems.value = List.from(contentItems);
      isLoading.value = false;
    });
  }

  void search(String query) {
    if (query.isEmpty) {
      applyFilters();
    } else {
      filteredItems.value = contentItems.where((item) {
        return item.content.toLowerCase().contains(query.toLowerCase()) ||
            item.reportedBy.toLowerCase().contains(query.toLowerCase()) ||
            item.reportReason.toLowerCase().contains(query.toLowerCase()) ||
            item.id.toString().contains(query);
      }).toList();
    }
  }

  void filterByStatus(String status) {
    selectedFilter.value = status;
    applyFilters();
  }

  void filterByType(String type) {
    selectedType.value = type;
    applyFilters();
  }

  void applyFilters() {
    var result = List<ContentItem>.from(contentItems);
    
    if (selectedFilter.value != 'All') {
      result = result.where((item) => item.status == selectedFilter.value).toList();
    }
    
    if (selectedType.value != 'All') {
      result = result.where((item) => item.type == selectedType.value).toList();
    }
    
    filteredItems.value = result;
  }

  void approveContent(int id) {
    final index = contentItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      final item = contentItems[index];
      final updatedItem = ContentItem(
        id: item.id,
        type: item.type,
        content: item.content,
        reportReason: item.reportReason,
        reportDate: item.reportDate,
        reportedBy: item.reportedBy,
        status: 'Approved',
        aiConfidence: item.aiConfidence,
      );
      contentItems[index] = updatedItem;
      applyFilters();
      Get.snackbar('Success', 'Content has been approved');
    }
  }

  void rejectContent(int id) {
    final index = contentItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      final item = contentItems[index];
      final updatedItem = ContentItem(
        id: item.id,
        type: item.type,
        content: item.content,
        reportReason: item.reportReason,
        reportDate: item.reportDate,
        reportedBy: item.reportedBy,
        status: 'Rejected',
        aiConfidence: item.aiConfidence,
      );
      contentItems[index] = updatedItem;
      applyFilters();
      Get.snackbar('Success', 'Content has been rejected');
    }
  }

  List<ContentItem> _generateMockContent() {
    final types = ['Profile Photo', 'Bio Text', 'Message', 'Comment'];
    final reasons = ['Inappropriate', 'Spam', 'Fake Profile', 'Harassment', 'Nudity'];
    final statuses = ['Pending', 'Approved', 'Rejected'];
    final users = ['John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis'];
    
    final mockContent = [
      'https://i.pravatar.cc/300?img=1',
      'https://i.pravatar.cc/300?img=2',
      'https://i.pravatar.cc/300?img=3',
      'Looking for fun tonight! DM me for a good time ;)',
      'Hey beautiful, want to meet up?',
      'I make \$10,000 per week working from home! Click my link to learn how!',
      'You look ugly in that photo lol',
      'I love hiking, reading, and spending time with my dog.',
      'https://i.pravatar.cc/300?img=4',
      'https://i.pravatar.cc/300?img=5',
    ];
    
    return List.generate(mockContent.length, (index) {
      final type = mockContent[index].startsWith('http') ? 'Profile Photo' : 'Bio Text';
      final reportReason = reasons[index % reasons.length];
      final status = statuses[index % statuses.length];
      final reportedBy = users[index % users.length];
      final aiConfidence = (index % 10) * 0.1;
      
      return ContentItem(
        id: 2000 + index,
        type: type,
        content: mockContent[index],
        reportReason: reportReason,
        reportDate: DateTime.now().subtract(Duration(hours: index * 5)),
        reportedBy: reportedBy,
        status: status,
        aiConfidence: aiConfidence,
      );
    });
  }
}