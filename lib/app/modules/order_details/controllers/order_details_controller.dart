import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/models/app_models.dart';

class OrderDetailsController extends GetxController {
  final RxBool isLoading = true.obs;
  final Rx<OrderModel?> order = Rx<OrderModel?>(null);
  final RxList<ChatMessageModel> chatMessages = <ChatMessageModel>[].obs;
  final RxInt selectedTabIndex = 0.obs;

  late int orderId;

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments['orderId'] ?? 0;
    loadOrderDetails();
  }

  void loadOrderDetails() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      order.value = _generateMockOrder(orderId);
      chatMessages.value = _generateMockChatMessages(orderId);
      isLoading.value = false;
    });
  }

  void changeTab(int index) {
    selectedTabIndex.value = index;
  }

  void openUserProfile(int userId) {
    Get.toNamed('/user-profile', arguments: {'userId': userId});
  }

  void openServiceDetails(int serviceId) {
    Get.toNamed('/service-details', arguments: {'serviceId': serviceId});
  }

  void updateOrderStatus(String newStatus) {
    Get.dialog(
      AlertDialog(
        title: Text('Update Order Status'),
        content: Text('Are you sure you want to change the status to $newStatus?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (order.value != null) {
                order.value = OrderModel(
                  id: order.value!.id,
                  serviceId: order.value!.serviceId,
                  serviceTitle: order.value!.serviceTitle,
                  buyerId: order.value!.buyerId,
                  buyerName: order.value!.buyerName,
                  buyerImage: order.value!.buyerImage,
                  sellerId: order.value!.sellerId,
                  sellerName: order.value!.sellerName,
                  sellerImage: order.value!.sellerImage,
                  amount: order.value!.amount,
                  currency: order.value!.currency,
                  status: newStatus,
                  createdAt: order.value!.createdAt,
                  completedAt: newStatus == 'completed' ? DateTime.now() : order.value!.completedAt,
                  dueDate: order.value!.dueDate,
                  requirements: order.value!.requirements,
                  deliverables: order.value!.deliverables,
                  buyerRating: order.value!.buyerRating,
                  sellerRating: order.value!.sellerRating,
                  buyerReview: order.value!.buyerReview,
                  sellerReview: order.value!.sellerReview,
                );
              }
              Get.snackbar('Success', 'Order status updated to $newStatus');
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  OrderModel _generateMockOrder(int orderId) {
    return OrderModel(
      id: orderId,
      serviceId: 101,
      serviceTitle: 'Professional Logo Design',
      buyerId: 2001,
      buyerName: 'John Smith',
      buyerImage: 'https://i.pravatar.cc/150?img=1',
      sellerId: 1001,
      sellerName: 'Jane Designer',
      sellerImage: 'https://i.pravatar.cc/150?img=2',
      amount: 150.0,
      currency: 'USD',
      status: 'active',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      completedAt: null,
      dueDate: DateTime.now().add(const Duration(days: 2)),
      requirements: 'I need a modern logo for my tech startup. The logo should be minimalist and professional.',
      deliverables: ['Logo in PNG format', 'Logo in SVG format', 'Brand guidelines'],
      buyerRating: null,
      sellerRating: null,
      buyerReview: null,
      sellerReview: null,
    );
  }

  List<ChatMessageModel> _generateMockChatMessages(int orderId) {
    return [
      ChatMessageModel(
        id: 1,
        orderId: orderId,
        senderId: 2001,
        senderName: 'John Smith',
        message: 'Hi! I\'m excited to work with you on this logo design.',
        timestamp: DateTime.now().subtract(const Duration(days: 5)),
        type: 'text',
        isRead: true,
      ),
      ChatMessageModel(
        id: 2,
        orderId: orderId,
        senderId: 1001,
        senderName: 'Jane Designer',
        message: 'Hello! Thank you for choosing me. I\'ll start working on your logo right away.',
        timestamp: DateTime.now().subtract(const Duration(days: 4, hours: 23)),
        type: 'text',
        isRead: true,
      ),
      ChatMessageModel(
        id: 3,
        orderId: orderId,
        senderId: 1001,
        senderName: 'Jane Designer',
        message: 'Here are some initial concepts for your review.',
        timestamp: DateTime.now().subtract(const Duration(days: 3)),
        type: 'image',
        attachmentUrl: 'https://picsum.photos/300/200?random=1',
        isRead: true,
      ),
      ChatMessageModel(
        id: 4,
        orderId: orderId,
        senderId: 2001,
        senderName: 'John Smith',
        message: 'These look great! I like the second one. Can you make some adjustments?',
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        type: 'text',
        isRead: true,
      ),
      ChatMessageModel(
        id: 5,
        orderId: orderId,
        senderId: 1001,
        senderName: 'Jane Designer',
        message: 'Absolutely! I\'ll make those changes and send you the updated version.',
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        type: 'text',
        isRead: false,
      ),
    ];
  }
}
