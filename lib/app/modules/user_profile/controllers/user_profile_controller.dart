import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/models/app_models.dart';

class UserProfileController extends GetxController {
  final RxBool isLoading = true.obs;
  final Rx<UserModel?> user = Rx<UserModel?>(null);
  final RxList<ServiceModel> userServices = <ServiceModel>[].obs;
  final RxList<OrderModel> userOrders = <OrderModel>[].obs;
  final RxList<ChatMessageModel> userChats = <ChatMessageModel>[].obs;
  final RxList<ReviewModel> userReviews = <ReviewModel>[].obs;
  final RxInt selectedTabIndex = 0.obs;

  late int userId;

  @override
  void onInit() {
    super.onInit();
    userId = Get.arguments['userId'] ?? 0;
    loadUserProfile();
  }

  void loadUserProfile() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      user.value = _generateMockUser(userId);
      userServices.value = _generateMockServices(userId);
      userOrders.value = _generateMockOrders(userId);
      userChats.value = _generateMockChats(userId);
      userReviews.value = _generateMockReviews(userId);
      isLoading.value = false;
    });
  }

  void changeTab(int index) {
    selectedTabIndex.value = index;
  }

  void banUser() {
    Get.dialog(
      AlertDialog(
        title: const Text('Ban User'),
        content: const Text('Are you sure you want to ban this user?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar('Success', 'User has been banned');
              // Update user status
              if (user.value != null) {
                user.value = UserModel(
                  id: user.value!.id,
                  name: user.value!.name,
                  email: user.value!.email,
                  phone: user.value!.phone,
                  status: 'Banned',
                  joinDate: user.value!.joinDate,
                  profileImage: user.value!.profileImage,
                  isVerified: user.value!.isVerified,
                  bio: user.value!.bio,
                  location: user.value!.location,
                  rating: user.value!.rating,
                  totalOrders: user.value!.totalOrders,
                  completedOrders: user.value!.completedOrders,
                  skills: user.value!.skills,
                  lastActive: user.value!.lastActive,
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Ban User'),
          ),
        ],
      ),
    );
  }

  void verifyUser() {
    Get.snackbar('Success', 'User has been verified');
    if (user.value != null) {
      user.value = UserModel(
        id: user.value!.id,
        name: user.value!.name,
        email: user.value!.email,
        phone: user.value!.phone,
        status: user.value!.status,
        joinDate: user.value!.joinDate,
        profileImage: user.value!.profileImage,
        isVerified: true,
        bio: user.value!.bio,
        location: user.value!.location,
        rating: user.value!.rating,
        totalOrders: user.value!.totalOrders,
        completedOrders: user.value!.completedOrders,
        skills: user.value!.skills,
        lastActive: user.value!.lastActive,
      );
    }
  }

  void openOrderDetails(int orderId) {
    Get.toNamed('/order-details', arguments: {'orderId': orderId});
  }

  void openServiceDetails(int serviceId) {
    Get.toNamed('/service-details', arguments: {'serviceId': serviceId});
  }

  UserModel _generateMockUser(int userId) {
    final names = ['John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis'];
    final name = names[userId % names.length];
    
    return UserModel(
      id: userId,
      name: name,
      email: '${name.toLowerCase().replaceAll(' ', '.')}@example.com',
      phone: '+1234567890',
      status: 'Active',
      joinDate: DateTime.now().subtract(Duration(days: userId * 30)),
      profileImage: 'https://i.pravatar.cc/150?img=$userId',
      isVerified: userId % 2 == 0,
      bio: 'Professional freelancer with expertise in multiple domains. Passionate about delivering high-quality work.',
      location: 'New York, USA',
      rating: 4.5 + (userId % 5) * 0.1,
      totalOrders: 50 + userId * 10,
      completedOrders: 45 + userId * 8,
      skills: ['Design', 'Development', 'Marketing', 'Writing'],
      lastActive: DateTime.now().subtract(Duration(hours: userId % 24)),
    );
  }

  List<ServiceModel> _generateMockServices(int userId) {
    return List.generate(5, (index) {
      return ServiceModel(
        id: userId * 100 + index,
        title: 'Professional Service ${index + 1}',
        description: 'High-quality service description for service ${index + 1}',
        category: ['Design', 'Development', 'Marketing', 'Writing'][index % 4],
        price: 50.0 + index * 25,
        currency: 'USD',
        deliveryTime: 3 + index,
        images: ['https://picsum.photos/300/200?random=${userId * 10 + index}'],
        sellerId: userId,
        sellerName: 'User $userId',
        sellerImage: 'https://i.pravatar.cc/150?img=$userId',
        rating: 4.0 + (index % 5) * 0.2,
        reviewCount: 10 + index * 5,
        status: 'active',
        createdAt: DateTime.now().subtract(Duration(days: index * 10)),
        tags: ['professional', 'quality', 'fast'],
      );
    });
  }

  List<OrderModel> _generateMockOrders(int userId) {
    final statuses = ['active', 'completed', 'in_progress', 'delivered'];
    
    return List.generate(10, (index) {
      return OrderModel(
        id: userId * 1000 + index,
        serviceId: userId * 100 + (index % 5),
        serviceTitle: 'Service Title ${index + 1}',
        buyerId: userId + 100,
        buyerName: 'Buyer ${index + 1}',
        buyerImage: 'https://i.pravatar.cc/150?img=${userId + 100}',
        sellerId: userId,
        sellerName: 'User $userId',
        sellerImage: 'https://i.pravatar.cc/150?img=$userId',
        amount: 100.0 + index * 50,
        currency: 'USD',
        status: statuses[index % statuses.length],
        createdAt: DateTime.now().subtract(Duration(days: index * 5)),
        completedAt: index % 2 == 0 ? DateTime.now().subtract(Duration(days: index * 2)) : null,
        dueDate: DateTime.now().add(Duration(days: 7 - index)),
        requirements: 'Order requirements for order ${index + 1}',
        deliverables: ['Deliverable 1', 'Deliverable 2'],
        buyerRating: index % 2 == 0 ? 4 + (index % 2) : null,
        sellerRating: index % 2 == 0 ? 5 : null,
        buyerReview: index % 2 == 0 ? 'Great work!' : null,
        sellerReview: index % 2 == 0 ? 'Excellent client!' : null,
      );
    });
  }

  List<ChatMessageModel> _generateMockChats(int userId) {
    return List.generate(20, (index) {
      return ChatMessageModel(
        id: index,
        orderId: userId * 1000 + (index % 10),
        senderId: index % 2 == 0 ? userId : userId + 100,
        senderName: index % 2 == 0 ? 'User $userId' : 'Buyer ${index + 1}',
        message: 'Chat message ${index + 1}',
        timestamp: DateTime.now().subtract(Duration(hours: index)),
        type: 'text',
        isRead: index % 3 != 0,
      );
    });
  }

  List<ReviewModel> _generateMockReviews(int userId) {
    return List.generate(8, (index) {
      return ReviewModel(
        id: index,
        orderId: userId * 1000 + index,
        reviewerId: userId + 100 + index,
        reviewerName: 'Reviewer ${index + 1}',
        reviewerImage: 'https://i.pravatar.cc/150?img=${userId + 100 + index}',
        revieweeId: userId,
        revieweeName: 'User $userId',
        rating: 4 + (index % 2),
        comment: 'Great service! Highly recommended.',
        createdAt: DateTime.now().subtract(Duration(days: index * 3)),
      );
    });
  }
}
