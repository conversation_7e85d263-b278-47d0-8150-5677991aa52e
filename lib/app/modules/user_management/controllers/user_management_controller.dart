import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserManagementController extends GetxController {
  final searchController = TextEditingController();
  final RxList<UserModel> users = <UserModel>[].obs;
  final RxList<UserModel> filteredUsers = <UserModel>[].obs;
  final RxBool isLoading = true.obs;
  final RxString selectedFilter = 'All'.obs;
  final RxInt selectedUserId = RxInt(-1);

  void banUser(int id) {
    final index = users.indexWhere((user) => user.id == id);
    if (index != -1) {
      final user = users[index];
      final updatedUser = UserModel(
        id: user.id,
        name: user.name,
        email: user.email,
        status: 'Banned',
        joinDate: user.joinDate,
        profileImage: user.profileImage,
        isVerified: user.isVerified,
      );
      users[index] = updatedUser;
      filterUsers(selectedFilter.value);
      Get.snackbar('Success', 'User has been banned');
    }
  }

  void filterUsers(String filter) {
    selectedFilter.value = filter;

    if (filter == 'All') {
      filteredUsers.value = List.from(users);
    } else if (filter == 'Active') {
      filteredUsers.value =
          users.where((user) => user.status == 'Active').toList();
    } else if (filter == 'Flagged') {
      filteredUsers.value =
          users.where((user) => user.status == 'Flagged').toList();
    } else if (filter == 'Banned') {
      filteredUsers.value =
          users.where((user) => user.status == 'Banned').toList();
    } else if (filter == 'Unverified') {
      filteredUsers.value = users.where((user) => !user.isVerified).toList();
    }
  }

  void loadUsers() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      users.value = _generateMockUsers();
      filteredUsers.value = List.from(users);
      isLoading.value = false;
    });
  }

  @override
  void onInit() {
    super.onInit();
    loadUsers();
  }

  void search(String query) {
    if (query.isEmpty) {
      filteredUsers.value = List.from(users);
    } else {
      filteredUsers.value =
          users.where((user) {
            return user.name.toLowerCase().contains(query.toLowerCase()) ||
                user.email.toLowerCase().contains(query.toLowerCase()) ||
                user.id.toString().contains(query);
          }).toList();
    }
  }

  void selectUser(int id) {
    selectedUserId.value = id;
    final user = users.firstWhere((user) => user.id == id);
    _showUserDetails(user);
  }

  void verifyUser(int id) {
    final index = users.indexWhere((user) => user.id == id);
    if (index != -1) {
      final user = users[index];
      final updatedUser = UserModel(
        id: user.id,
        name: user.name,
        email: user.email,
        status: user.status,
        joinDate: user.joinDate,
        profileImage: user.profileImage,
        isVerified: true,
      );
      users[index] = updatedUser;
      filterUsers(selectedFilter.value);
      Get.snackbar('Success', 'User has been verified');
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  List<UserModel> _generateMockUsers() {
    final statuses = ['Active', 'Flagged', 'Banned'];
    final names = [
      'John Doe',
      'Jane Smith',
      'Robert Johnson',
      'Emily Davis',
      'Michael Brown',
      'Sarah Wilson',
      'David Miller',
      'Lisa Moore',
      'James Taylor',
      'Jennifer Anderson',
      'Thomas White',
      'Jessica Martinez',
      'Daniel Garcia',
      'Patricia Robinson',
      'Christopher Lee',
      'Nancy Hall',
      'Matthew Young',
      'Karen Allen',
      'Anthony Wright',
      'Betty King',
    ];

    return List.generate(20, (index) {
      final name = names[index];
      final email = '${name.toLowerCase().replaceAll(' ', '.')}@example.com';
      final status = statuses[index % 3];
      final isVerified = index % 4 != 0;

      return UserModel(
        id: 1000 + index,
        name: name,
        email: email,
        status: status,
        joinDate: DateTime.now().subtract(Duration(days: index * 7)),
        profileImage: 'https://i.pravatar.cc/150?img=${index + 1}',
        isVerified: isVerified,
      );
    });
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Active':
        return Colors.green;
      case 'Flagged':
        return Colors.orange;
      case 'Banned':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showUserDetails(UserModel user) {
    Get.dialog(
      Dialog(
        child: Container(
          width: Get.width > 600 ? 600 : Get.width * 0.9,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'User Details',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(user.profileImage),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user.email,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(user.status),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            user.status,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const Divider(),
              const SizedBox(height: 16),
              _buildDetailRow('User ID', '#${user.id}'),
              _buildDetailRow(
                'Join Date',
                '${user.joinDate.day}/${user.joinDate.month}/${user.joinDate.year}',
              ),
              _buildDetailRow(
                'Verification Status',
                user.isVerified ? 'Verified' : 'Unverified',
              ),
              _buildDetailRow('Account Status', user.status),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (!user.isVerified)
                    ElevatedButton.icon(
                      onPressed: () {
                        Get.back();
                        verifyUser(user.id);
                      },
                      icon: const Icon(Icons.check_circle),
                      label: const Text('Verify User'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  const SizedBox(width: 8),
                  if (user.status != 'Banned')
                    ElevatedButton.icon(
                      onPressed: () {
                        Get.back();
                        banUser(user.id);
                      },
                      icon: const Icon(Icons.block),
                      label: const Text('Ban User'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UserModel {
  final int id;
  final String name;
  final String email;
  final String status;
  final DateTime joinDate;
  final String profileImage;
  final bool isVerified;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.status,
    required this.joinDate,
    required this.profileImage,
    required this.isVerified,
  });
}
