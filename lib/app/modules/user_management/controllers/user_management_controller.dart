import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserModel {
  final int id;
  final String name;
  final String email;
  final String status;
  final DateTime joinDate;
  final String profileImage;
  final bool isVerified;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.status,
    required this.joinDate,
    required this.profileImage,
    required this.isVerified,
  });
}

class UserManagementController extends GetxController {
  final searchController = TextEditingController();
  final RxList<UserModel> users = <UserModel>[].obs;
  final RxList<UserModel> filteredUsers = <UserModel>[].obs;
  final RxBool isLoading = true.obs;
  final RxString selectedFilter = 'All'.obs;
  final RxInt selectedUserId = RxInt(-1);

  @override
  void onInit() {
    super.onInit();
    loadUsers();
  }

  void loadUsers() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      users.value = _generateMockUsers();
      filteredUsers.value = List.from(users);
      isLoading.value = false;
    });
  }

  void search(String query) {
    if (query.isEmpty) {
      filteredUsers.value = List.from(users);
    } else {
      filteredUsers.value = users.where((user) {
        return user.name.toLowerCase().contains(query.toLowerCase()) ||
            user.email.toLowerCase().contains(query.toLowerCase()) ||
            user.id.toString().contains(query);
      }).toList();
    }
  }

  void filterUsers(String filter) {
    selectedFilter.value = filter;
    
    if (filter == 'All') {
      filteredUsers.value = List.from(users);
    } else if (filter == 'Active') {
      filteredUsers.value = users.where((user) => user.status == 'Active').toList();
    } else if (filter == 'Flagged') {
      filteredUsers.value = users.where((user) => user.status == 'Flagged').toList();
    } else if (filter == 'Banned') {
      filteredUsers.value = users.where((user) => user.status == 'Banned').toList();
    } else if (filter == 'Unverified') {
      filteredUsers.value = users.where((user) => !user.isVerified).toList();
    }
  }

  void selectUser(int id) {
    selectedUserId.value = id;
  }

  void banUser(int id) {
    final index = users.indexWhere((user) => user.id == id);
    if (index != -1) {
      final user = users[index];
      final updatedUser = UserModel(
        id: user.id,
        name: user.name,
        email: user.email,
        status: 'Banned',
        joinDate: user.joinDate,
        profileImage: user.profileImage,
        isVerified: user.isVerified,
      );
      users[index] = updatedUser;
      filterUsers(selectedFilter.value);
      Get.snackbar('Success', 'User has been banned');
    }
  }

  void verifyUser(int id) {
    final index = users.indexWhere((user) => user.id == id);
    if (index != -1) {
      final user = users[index];
      final updatedUser = UserModel(
        id: user.id,
        name: user.name,
        email: user.email,
        status: user.status,
        joinDate: user.joinDate,
        profileImage: user.profileImage,
        isVerified: true,
      );
      users[index] = updatedUser;
      filterUsers(selectedFilter.value);
      Get.snackbar('Success', 'User has been verified');
    }
  }

  List<UserModel> _generateMockUsers() {
    final statuses = ['Active', 'Flagged', 'Banned'];
    final names = [
      'John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis',
      'Michael Brown', 'Sarah Wilson', 'David Miller', 'Lisa Moore',
      'James Taylor', 'Jennifer Anderson', 'Thomas White', 'Jessica Martinez',
      'Daniel Garcia', 'Patricia Robinson', 'Christopher Lee', 'Nancy Hall',
      'Matthew Young', 'Karen Allen', 'Anthony Wright', 'Betty King'
    ];
    
    return List.generate(20, (index) {
      final name = names[index];
      final email = name.toLowerCase().replaceAll(' ', '.') + '@example.com';
      final status = statuses[index % 3];
      final isVerified = index % 4 != 0;
      
      return UserModel(
        id: 1000 + index,
        name: name,
        email: email,
        status: status,
        joinDate: DateTime.now().subtract(Duration(days: index * 7)),
        profileImage: 'https://i.pravatar.cc/150?img=${index + 1}',
        isVerified: isVerified,
      );
    });
  }
}