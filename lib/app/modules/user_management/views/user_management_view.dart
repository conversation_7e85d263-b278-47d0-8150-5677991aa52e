import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';
import 'package:greet_admin/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:greet_admin/app/widgets/app_header.dart';
import 'package:greet_admin/app/widgets/sidebar.dart';

import '../controllers/user_management_controller.dart';

class UserManagementView extends GetView<UserManagementController> {
  const UserManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    final dashboardController = Get.find<DashboardController>();

    return Scaffold(
      key: dashboardController.scaffoldKey,
      drawer: Get.width < 1200 ? const Sidebar() : null,
      body: Row(
        children: [
          // Sidebar for large screens
          if (Get.width >= 1200) const Sidebar(),

          // Main content
          Expanded(
            child: Column(
              children: [
                // Header
                const AppHeader(title: 'User Management'),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Search and filter bar
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: controller.searchController,
                                decoration: InputDecoration(
                                  hintText: 'Search users...',
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                onChanged: controller.search,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Obx(
                              () => DropdownButton<String>(
                                value: controller.selectedFilter.value,
                                items:
                                    [
                                      'All',
                                      'Active',
                                      'Flagged',
                                      'Banned',
                                      'Unverified',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.filterUsers(value);
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Users table
                        Expanded(
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (controller.filteredUsers.isEmpty) {
                              return const Center(
                                child: Text('No users found'),
                              );
                            }

                            return Card(
                              elevation: 2,
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: DataTable(
                                  columns: const [
                                    DataColumn(label: Text('ID')),
                                    DataColumn(label: Text('User')),
                                    DataColumn(label: Text('Email')),
                                    DataColumn(label: Text('Status')),
                                    DataColumn(label: Text('Join Date')),
                                    DataColumn(label: Text('Verified')),
                                    DataColumn(label: Text('Actions')),
                                  ],
                                  rows:
                                      controller.filteredUsers.map((user) {
                                        return DataRow(
                                          cells: [
                                            DataCell(Text('#${user.id}')),
                                            DataCell(
                                              Row(
                                                children: [
                                                  CircleAvatar(
                                                    radius: 16,
                                                    backgroundImage:
                                                        NetworkImage(
                                                          user.profileImage,
                                                        ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(user.name),
                                                ],
                                              ),
                                            ),
                                            DataCell(Text(user.email)),
                                            DataCell(
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                decoration: BoxDecoration(
                                                  color: _getStatusColor(
                                                    user.status,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  user.status,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            DataCell(
                                              Text(
                                                '${user.joinDate.day}/${user.joinDate.month}/${user.joinDate.year}',
                                              ),
                                            ),
                                            DataCell(
                                              user.isVerified
                                                  ? const Icon(
                                                    Icons.verified,
                                                    color: Colors.green,
                                                  )
                                                  : const Icon(
                                                    Icons.cancel,
                                                    color: Colors.red,
                                                  ),
                                            ),
                                            DataCell(
                                              Row(
                                                children: [
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.visibility,
                                                    ),
                                                    onPressed:
                                                        () => controller
                                                            .selectUser(
                                                              user.id,
                                                            ),
                                                    tooltip: 'View Details',
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.block,
                                                    ),
                                                    onPressed:
                                                        () => controller
                                                            .banUser(user.id),
                                                    tooltip: 'Ban User',
                                                  ),
                                                  if (!user.isVerified)
                                                    IconButton(
                                                      icon: const Icon(
                                                        Icons.check_circle,
                                                      ),
                                                      onPressed:
                                                          () => controller
                                                              .verifyUser(
                                                                user.id,
                                                              ),
                                                      tooltip: 'Verify User',
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        );
                                      }).toList(),
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Active':
        return AppColors.successColor;
      case 'Flagged':
        return AppColors.warningColor;
      case 'Banned':
        return AppColors.errorColor;
      default:
        return AppColors.clrGrey;
    }
  }
}
