import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ReportModel {
  final int id;
  final String type;
  final String reportedUser;
  final String reportingUser;
  final String reason;
  final DateTime date;
  final String status;
  final String details;

  ReportModel({
    required this.id,
    required this.type,
    required this.reportedUser,
    required this.reportingUser,
    required this.reason,
    required this.date,
    required this.status,
    required this.details,
  });
}

class ReportsController extends GetxController {
  final searchController = TextEditingController();
  final RxList<ReportModel> reports = <ReportModel>[].obs;
  final RxList<ReportModel> filteredReports = <ReportModel>[].obs;
  final RxBool isLoading = true.obs;
  final RxString selectedFilter = 'All'.obs;
  final RxString selectedType = 'All'.obs;

  @override
  void onInit() {
    super.onInit();
    loadReports();
  }

  void loadReports() {
    isLoading.value = true;
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      reports.value = _generateMockReports();
      filteredReports.value = List.from(reports);
      isLoading.value = false;
    });
  }

  void search(String query) {
    if (query.isEmpty) {
      applyFilters();
    } else {
      filteredReports.value = reports.where((report) {
        return report.reportedUser.toLowerCase().contains(query.toLowerCase()) ||
            report.reportingUser.toLowerCase().contains(query.toLowerCase()) ||
            report.reason.toLowerCase().contains(query.toLowerCase()) ||
            report.details.toLowerCase().contains(query.toLowerCase()) ||
            report.id.toString().contains(query);
      }).toList();
    }
  }

  void filterByStatus(String status) {
    selectedFilter.value = status;
    applyFilters();
  }

  void filterByType(String type) {
    selectedType.value = type;
    applyFilters();
  }

  void applyFilters() {
    var result = List<ReportModel>.from(reports);
    
    if (selectedFilter.value != 'All') {
      result = result.where((report) => report.status == selectedFilter.value).toList();
    }
    
    if (selectedType.value != 'All') {
      result = result.where((report) => report.type == selectedType.value).toList();
    }
    
    filteredReports.value = result;
  }

  void resolveReport(int id) {
    final index = reports.indexWhere((report) => report.id == id);
    if (index != -1) {
      final report = reports[index];
      final updatedReport = ReportModel(
        id: report.id,
        type: report.type,
        reportedUser: report.reportedUser,
        reportingUser: report.reportingUser,
        reason: report.reason,
        date: report.date,
        status: 'Resolved',
        details: report.details,
      );
      reports[index] = updatedReport;
      applyFilters();
      Get.snackbar('Success', 'Report has been resolved');
    }
  }

  void dismissReport(int id) {
    final index = reports.indexWhere((report) => report.id == id);
    if (index != -1) {
      final report = reports[index];
      final updatedReport = ReportModel(
        id: report.id,
        type: report.type,
        reportedUser: report.reportedUser,
        reportingUser: report.reportingUser,
        reason: report.reason,
        date: report.date,
        status: 'Dismissed',
        details: report.details,
      );
      reports[index] = updatedReport;
      applyFilters();
      Get.snackbar('Success', 'Report has been dismissed');
    }
  }

  List<ReportModel> _generateMockReports() {
    final types = ['Harassment', 'Inappropriate Content', 'Fake Profile', 'Spam', 'Other'];
    final reasons = ['Offensive messages', 'Inappropriate photos', 'Fake identity', 'Spam messages', 'Threatening behavior'];
    final statuses = ['Pending', 'In Review', 'Resolved', 'Dismissed'];
    final users = [
      'John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis',
      'Michael Brown', 'Sarah Wilson', 'David Miller', 'Lisa Moore'
    ];
    
    return List.generate(20, (index) {
      final type = types[index % types.length];
      final reason = reasons[index % reasons.length];
      final status = statuses[index % statuses.length];
      final reportedUser = users[(index + 1) % users.length];
      final reportingUser = users[index % users.length];
      
      return ReportModel(
        id: 3000 + index,
        type: type,
        reportedUser: reportedUser,
        reportingUser: reportingUser,
        reason: reason,
        date: DateTime.now().subtract(Duration(days: index)),
        status: status,
        details: 'This user was reported for ${reason.toLowerCase()}. The incident occurred on ${DateTime.now().subtract(Duration(days: index + 1)).toString().substring(0, 10)}.',
      );
    });
  }
}