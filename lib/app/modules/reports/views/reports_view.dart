import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:greet_admin/app/core/constants/app_colors.dart';
import 'package:greet_admin/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:greet_admin/app/widgets/app_header.dart';
import 'package:greet_admin/app/widgets/sidebar.dart';

import '../controllers/reports_controller.dart';

class ReportsView extends GetView<ReportsController> {
  const ReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    final dashboardController = Get.find<DashboardController>();

    return Scaffold(
      key: dashboardController.scaffoldKey,
      drawer: Get.width < 1200 ? const Sidebar() : null,
      body: Row(
        children: [
          // Sidebar for large screens
          if (Get.width >= 1200) const Sidebar(),

          // Main content
          Expanded(
            child: Column(
              children: [
                // Header
                const AppHeader(title: 'User Reports'),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Search and filter bar
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: controller.searchController,
                                decoration: InputDecoration(
                                  hintText: 'Search reports...',
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                onChanged: controller.search,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Obx(
                              () => DropdownButton<String>(
                                value: controller.selectedFilter.value,
                                items:
                                    [
                                      'All',
                                      'Pending',
                                      'In Review',
                                      'Resolved',
                                      'Dismissed',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.filterByStatus(value);
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Obx(
                              () => DropdownButton<String>(
                                value: controller.selectedType.value,
                                items:
                                    [
                                      'All',
                                      'Harassment',
                                      'Inappropriate Content',
                                      'Fake Profile',
                                      'Spam',
                                      'Other',
                                    ].map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.filterByType(value);
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Reports table
                        Expanded(
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (controller.filteredReports.isEmpty) {
                              return const Center(
                                child: Text('No reports found'),
                              );
                            }

                            return Card(
                              elevation: 2,
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: DataTable(
                                  columns: const [
                                    DataColumn(label: Text('ID')),
                                    DataColumn(label: Text('Type')),
                                    DataColumn(label: Text('Reported User')),
                                    DataColumn(label: Text('Reporting User')),
                                    DataColumn(label: Text('Reason')),
                                    DataColumn(label: Text('Date')),
                                    DataColumn(label: Text('Status')),
                                    DataColumn(label: Text('Actions')),
                                  ],
                                  rows:
                                      controller.filteredReports.map((report) {
                                        return DataRow(
                                          cells: [
                                            DataCell(Text('#${report.id}')),
                                            DataCell(Text(report.type)),
                                            DataCell(Text(report.reportedUser)),
                                            DataCell(
                                              Text(report.reportingUser),
                                            ),
                                            DataCell(Text(report.reason)),
                                            DataCell(
                                              Text(
                                                '${report.date.day}/${report.date.month}/${report.date.year}',
                                              ),
                                            ),
                                            DataCell(
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                decoration: BoxDecoration(
                                                  color: _getStatusColor(
                                                    report.status,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  report.status,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            DataCell(
                                              Row(
                                                children: [
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.visibility,
                                                    ),
                                                    onPressed: () {
                                                      // Show report details
                                                      Get.dialog(
                                                        AlertDialog(
                                                          title: Text(
                                                            'Report #${report.id}',
                                                          ),
                                                          content: SizedBox(
                                                            width: 500,
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  'Type: ${report.type}',
                                                                ),
                                                                Text(
                                                                  'Reported User: ${report.reportedUser}',
                                                                ),
                                                                Text(
                                                                  'Reporting User: ${report.reportingUser}',
                                                                ),
                                                                Text(
                                                                  'Reason: ${report.reason}',
                                                                ),
                                                                Text(
                                                                  'Date: ${report.date.day}/${report.date.month}/${report.date.year}',
                                                                ),
                                                                Text(
                                                                  'Status: ${report.status}',
                                                                ),
                                                                const SizedBox(
                                                                  height: 16,
                                                                ),
                                                                const Text(
                                                                  'Details:',
                                                                ),
                                                                const SizedBox(
                                                                  height: 8,
                                                                ),
                                                                Text(
                                                                  report
                                                                      .details,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          actions: [
                                                            TextButton(
                                                              onPressed:
                                                                  () =>
                                                                      Get.back(),
                                                              child: const Text(
                                                                'Close',
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                    tooltip: 'View Details',
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.check_circle,
                                                    ),
                                                    onPressed:
                                                        () => controller
                                                            .resolveReport(
                                                              report.id,
                                                            ),
                                                    tooltip: 'Resolve',
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.cancel,
                                                    ),
                                                    onPressed:
                                                        () => controller
                                                            .dismissReport(
                                                              report.id,
                                                            ),
                                                    tooltip: 'Dismiss',
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        );
                                      }).toList(),
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Pending':
        return AppColors.warningColor;
      case 'In Review':
        return AppColors.infoColor;
      case 'Resolved':
        return AppColors.successColor;
      case 'Dismissed':
        return AppColors.clrGrey;
      default:
        return AppColors.clrGrey;
    }
  }
}
